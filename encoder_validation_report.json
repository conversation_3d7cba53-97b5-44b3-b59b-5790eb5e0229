{"summary": {"total_tests": 2, "encoding_successes": 2, "decoding_successes": 0, "round_trip_successes": 0, "ast_equivalences": 0, "encoding_success_rate": 1.0, "decoding_success_rate": 0.0, "round_trip_success_rate": 0.0, "ast_equivalence_rate": 0.0, "avg_encoding_time": 0.004524646000000132, "avg_decoding_time": 0.0033831874999998846, "avg_compression_ratio": 0.22201425475164802, "avg_symbols_count": 85.0}, "compression_comparison": {"neuroglyph_ratio": 0.22201425475164802, "gzip_ratio": 0.3847053028736127, "brotli_ratio": 0.3206570855752421, "vs_gzip_factor": 1.7327955058739626, "vs_brotli_factor": 1.4443085464667076}, "file_size_analysis": {"small_files": {"count": 1, "avg_compression": 0.3705206220419202, "ast_equivalence_rate": 0.0}, "medium_files": {"count": 1, "avg_compression": 0.07350788746137583, "ast_equivalence_rate": 0.0}, "large_files": {"count": 0, "avg_compression": 0, "ast_equivalence_rate": 0}}, "detailed_results": [{"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_yzpkkwb1/scikit-learn-main/sklearn/utils/optimize.py", "file_size": 12298, "lines_count": 390, "encoding_success": true, "encoding_time": 0.006889417000000009, "compressed_size": 904, "compression_ratio": 0.07350788746137583, "symbols_count": 102, "decoding_success": false, "decoding_time": 0.005574874999999757, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": 1423, "gzip_size": 3941, "brotli_size": 3430, "gzip_ratio": 0.3204586111562856, "brotli_ratio": 0.27890713937225564, "error_message": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_yzpkkwb1/scikit-learn-main/sklearn/utils/_missing.py", "file_size": 1479, "lines_count": 69, "encoding_success": true, "encoding_time": 0.0021598750000002553, "compressed_size": 548, "compression_ratio": 0.3705206220419202, "symbols_count": 68, "decoding_success": false, "decoding_time": 0.001191500000000012, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": 95, "gzip_size": 664, "brotli_size": 536, "gzip_ratio": 0.4489519945909398, "brotli_ratio": 0.36240703177822853, "error_message": null}]}